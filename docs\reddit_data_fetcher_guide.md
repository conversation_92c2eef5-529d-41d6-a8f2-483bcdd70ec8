# Reddit社交媒体数据获取指南

## 概述

Reddit数据获取脚本是一个专门用于从Reddit获取股票相关社交媒体数据的工具。它能够根据股票代码和日期，从多个Reddit子版块中搜索和提取相关的讨论内容。

## 主要功能

### 🎯 核心功能
- **智能股票匹配**: 根据股票代码自动匹配公司名称和别名
- **多源数据获取**: 支持从多个Reddit子版块获取数据
- **时间范围查询**: 支持指定日期和回溯天数
- **数据质量过滤**: 自动过滤和排序相关内容
- **情绪分析**: 基于关键词的简单情绪分析
- **结果保存**: 支持JSON和文本格式保存

### 📊 支持的Reddit子版块
- `stocks` - 股票讨论
- `investing` - 投资分析
- `wallstreetbets` - 散户投资者讨论
- `SecurityAnalysis` - 证券分析
- `ValueInvesting` - 价值投资
- `StockMarket` - 股市讨论
- `pennystocks` - 小盘股
- `options` - 期权交易

## 数据结构

### 📁 目录结构
```
data/
└── reddit_data/
    └── company_news/
        ├── stocks/
        │   └── 2024-05-20.jsonl
        ├── investing/
        │   └── 2024-05-20.jsonl
        ├── wallstreetbets/
        │   └── 2024-05-20.jsonl
        └── [其他subreddit]/
            └── [日期].jsonl
```

### 📄 JSONL文件格式
每行包含一个JSON对象，表示一个Reddit帖子：
```json
{
  "created_utc": 1716206400,
  "title": "AAPL earnings beat expectations!",
  "selftext": "Apple just reported strong quarterly results...",
  "url": "https://reddit.com/r/stocks/sample1",
  "ups": 456,
  "num_comments": 67,
  "subreddit": "stocks"
}
```

## 使用方法

### 🚀 命令行使用

#### 基本用法
```bash
# 获取AAPL在指定日期的Reddit讨论
python scripts/reddit_data_fetcher.py --ticker AAPL --date 2024-05-20

# 获取TSLA过去3天的讨论
python scripts/reddit_data_fetcher.py --ticker TSLA --date 2024-05-20 --days 3

# 获取NVDA讨论并保存结果
python scripts/reddit_data_fetcher.py --ticker NVDA --date 2024-05-20 --limit 10 --save
```

#### 高级选项
```bash
# 静默模式获取数据
python scripts/reddit_data_fetcher.py --ticker MSFT --date 2024-05-20 --quiet

# 包含情绪分析
python scripts/reddit_data_fetcher.py --ticker AAPL --date 2024-05-20 --sentiment

# 指定输出目录
python scripts/reddit_data_fetcher.py --ticker TSLA --date 2024-05-20 --output ./my_results --save
```

### 📋 参数说明

| 参数 | 简写 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--ticker` | `-t` | ✅ | - | 股票代码 (如: AAPL, TSLA) |
| `--date` | `-d` | ✅ | - | 目标日期 (YYYY-MM-DD) |
| `--days` | | ❌ | 1 | 向前查找的天数 |
| `--limit` | `-l` | ❌ | 5 | 每个subreddit的最大帖子数 |
| `--data-dir` | | ❌ | data/ | 数据目录路径 |
| `--output` | `-o` | ❌ | data/reddit_analysis | 输出目录 |
| `--save` | | ❌ | False | 保存结果到文件 |
| `--quiet` | `-q` | ❌ | False | 静默模式 |
| `--sentiment` | | ❌ | False | 显示情绪分析 |

### 🐍 Python API使用

```python
from scripts.reddit_data_fetcher import RedditDataFetcher

# 初始化获取器
fetcher = RedditDataFetcher(debug=True)

# 获取Reddit数据
results = fetcher.fetch_reddit_posts(
    ticker="AAPL",
    target_date="2024-05-20",
    days_back=1,
    max_posts_per_subreddit=5
)

# 显示结果
fetcher.display_results(results)

# 情绪分析
if results.get('success'):
    sentiment = fetcher.get_sentiment_summary(results)
    print(f"整体情绪: {sentiment['overall_sentiment']}")

# 保存结果
if results.get('success'):
    filepath = fetcher.save_results(results)
    print(f"结果已保存到: {filepath}")
```

## 股票代码映射

### 🏷️ 支持的股票代码
脚本内置了常见股票代码到公司名称的映射：

| 股票代码 | 公司名称/搜索关键词 |
|----------|-------------------|
| AAPL | Apple |
| TSLA | Tesla |
| NVDA | Nvidia |
| META | Meta OR Facebook |
| GOOGL | Google |
| MSFT | Microsoft |
| AMZN | Amazon |
| TSM | Taiwan Semiconductor Manufacturing Company OR TSMC |
| JPM | JPMorgan Chase OR JP Morgan |
| ... | ... |

### 🔍 搜索逻辑
1. 使用股票代码本身作为关键词
2. 添加对应的公司名称
3. 如果有多个名称（用"OR"分隔），全部添加
4. 在帖子标题和内容中搜索这些关键词

## 输出格式

### 📊 控制台输出示例
```
📊 Reddit数据获取结果
============================================================
🏷️  股票代码: AAPL
📅 目标日期: 2024-05-20
📆 查找天数: 1
🔍 搜索关键词: AAPL, Apple
📝 总帖子数: 8
⏱️  处理时间: 0.15秒

📋 Subreddit分布:
   wallstreetbets: 2 个帖子
   investing: 2 个帖子
   stocks: 2 个帖子
   SecurityAnalysis: 2 个帖子

📖 帖子详情 (显示前5个):
------------------------------------------------------------

1. 【wallstreetbets】AAPL to the moon! 🚀🚀🚀
   ⬆️  1250 赞 | 💬 89 评论
   📅 2024-05-20 11:00:00
   📄 Apple earnings beat expectations. This stock is going to explode! Diamond hands! 💎🙌
   🔗 https://reddit.com/r/wallstreetbets/sample1
```

### 📄 JSON输出格式
```json
{
  "success": true,
  "ticker": "AAPL",
  "target_date": "2024-05-20",
  "days_back": 1,
  "search_terms": ["AAPL", "Apple"],
  "posts": [
    {
      "title": "AAPL to the moon! 🚀🚀🚀",
      "content": "Apple earnings beat expectations...",
      "url": "https://reddit.com/r/wallstreetbets/sample1",
      "upvotes": 1250,
      "comments": 89,
      "subreddit": "wallstreetbets",
      "posted_date": "2024-05-20 11:00:00",
      "timestamp": 1716206400
    }
  ],
  "total_posts": 8,
  "subreddit_stats": {
    "wallstreetbets": 2,
    "investing": 2,
    "stocks": 2,
    "SecurityAnalysis": 2
  },
  "processing_time": 0.15,
  "timestamp": "2024-05-20T16:30:00"
}
```

### 📈 情绪分析输出
```
📈 情绪分析摘要:
------------------------------
总帖子数: 8
正面帖子: 5
负面帖子: 2
中性帖子: 1
整体情绪: positive
总赞数: 3456
平均赞数: 432.0
```

## 情绪分析

### 🎯 分析方法
基于关键词的简单情绪分析：

**正面关键词**:
- 英文: bullish, buy, moon, rocket, strong, good, great, excellent, positive, up, rise, gain, profit
- 中文: 看涨, 买入, 上涨, 利好, 强势, 看好

**负面关键词**:
- 英文: bearish, sell, short, weak, bad, terrible, negative, down, fall, loss, crash
- 中文: 看跌, 卖出, 下跌, 利空, 弱势, 看空

### 📊 评分逻辑
1. 统计每个帖子中正面和负面关键词数量
2. 比较正负关键词数量确定帖子情绪
3. 汇总所有帖子得出整体情绪
4. 计算各种统计指标

## 示例和测试

### 🧪 快速测试
```bash
# 运行测试脚本
python test_reddit_fetcher.py
```

### 📖 使用示例
```bash
# 运行示例脚本
python examples/reddit_data_example.py
```

### 🔍 查看帮助
```bash
python scripts/reddit_data_fetcher.py --help
```

## 数据准备

### 📁 自动创建示例数据
如果没有真实的Reddit数据，脚本会自动创建示例数据结构和内容用于演示。

### 📥 添加真实数据
要使用真实的Reddit数据，需要：
1. 准备JSONL格式的Reddit帖子数据
2. 按日期和subreddit组织文件
3. 放置在正确的目录结构中

### 📋 数据格式要求
每个JSONL文件中的帖子必须包含以下字段：
- `created_utc`: Unix时间戳
- `title`: 帖子标题
- `selftext`: 帖子内容
- `url`: 帖子链接
- `ups`: 赞数
- `num_comments`: 评论数
- `subreddit`: 子版块名称

## 故障排除

### 常见问题

1. **数据目录不存在**
   ```
   ⚠️ Reddit数据目录不存在: /path/to/reddit_data
   ```
   解决方案: 脚本会自动创建示例数据结构

2. **未找到相关帖子**
   ```
   📖 未找到相关帖子
   ```
   解决方案: 检查股票代码映射，或添加真实数据

3. **日期格式错误**
   ```
   ❌ 日期格式错误，请使用 YYYY-MM-DD 格式
   ```
   解决方案: 确保日期格式为YYYY-MM-DD

4. **JSON解析错误**
   ```
   ⚠️ JSON解析错误 (行X): ...
   ```
   解决方案: 检查JSONL文件格式是否正确

### 调试技巧

1. 使用`--debug`模式查看详细信息
2. 检查生成的示例数据文件
3. 验证股票代码映射是否正确
4. 确认日期范围内有数据文件

## 扩展开发

### 添加新的股票代码
在`_load_ticker_mapping`方法中添加新的映射：
```python
mapping["NEW_TICKER"] = "Company Name OR Alternative Name"
```

### 添加新的subreddit
在`supported_subreddits`列表中添加：
```python
self.supported_subreddits.append("new_subreddit")
```

### 自定义情绪分析
修改`get_sentiment_summary`方法中的关键词列表和评分逻辑。

### 集成到其他系统
脚本设计为独立运行，但也可以作为模块导入使用。
